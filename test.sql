SELECT 
    `a`.`teacher_id`, 
    `a`.`teacher_name`, 
    `a`.`dept_name`, 
    `a`.`status`, 
    COUNT(DISTINCT FROM_UNIXTIME(a.plan_time, "%Y-%m-%d")) AS `date`, 
    SUM(a.amount) AS total_amount, 
    SUM(a.new_amount) AS new_amount, 
    COUNT(DISTINCT a.new_cus_id) AS new_cus_count, 
    ROUND(SUM(a.new_amount) / IF(COUNT(DISTINCT a.new_cus_id) > 0, COUNT(DISTINCT a.new_cus_id), 1), 2) AS new_price, 
    SUM(a.old_amount) AS old_amount, 
    COUNT(DISTINCT a.old_cus_id) AS old_cus_count, 
    ROUND(SUM(a.old_amount) / IF(COUNT(DISTINCT a.old_cus_id) > 0, COUNT(DISTINCT a.old_cus_id), 1), 2) AS old_price, 
    COUNT(DISTINCT a.loss_cus_id) AS loss_num, 
    ROUND(SUM(a.amount) / IF((COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)) > 0, (COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)), 1), 2) AS total_price, 
    SUM(a.operate_amount) AS operate_amount 
FROM (
    (
        SELECT 
            `m`.`id` AS `teacher_id`, 
            `m`.`username` AS `teacher_name`, 
            `s`.`store_name` AS `dept_name`, 
            `m`.`status`, 
            `oh`.`plan_time`, 
            IFNULL((op.received_amount + op.group_amount), 0) AS amount, 
            CASE 
                WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") THEN oh.cus_id 
                ELSE NULL 
            END AS new_cus_id, 
            CASE 
                WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") THEN (op.sales_amount + op.group_amount) 
                ELSE 0 
            END AS new_amount, 
            CASE 
                WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") > FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") THEN oh.cus_id 
                ELSE NULL 
            END AS old_cus_id, 
            CASE 
                WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") > FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") THEN (op.sales_amount + op.group_amount) 
                ELSE 0 
            END AS old_amount, 
            NULL AS loss_cus_id, 
            CASE 
                WHEN cpr.id IS NOT NULL THEN (op.operation_amount + op.group_amount) 
                ELSE 0 
            END AS operate_amount 
        FROM `erp_order_header` `oh` 
        LEFT JOIN `erp_order_project` `op` ON op.order_id = oh.id 
        LEFT JOIN `erp_order_teacher` `ot` ON ot.order_id = oh.id AND ot.order_project_id = op.id 
        LEFT JOIN `erp_backend_member` `m` ON m.id = ot.user_id 
        LEFT JOIN `erp_customer` `c` ON c.id = oh.cus_id 
        LEFT JOIN `erp_store` `s` ON s.id = oh.store_id 
        LEFT JOIN `erp_customer_product_record` `cpr` ON cpr.order_id = oh.id AND cpr.goods_id = op.goods_id 
        WHERE (`oh`.`plan_time` BETWEEN '1704038400' AND '1748534399') 
        AND (`oh`.`order_status`=5) 
        AND (`ot`.`type`=1) 
        AND (`oh`.`entity_id`=1)
    ) 
    UNION ALL 
    (
        SELECT 
            `m`.`id` AS `teacher_id`, 
            `m`.`username` AS `teacher_name`, 
            `s`.`store_name` AS `dept_name`, 
            `m`.`status`, 
            `ccr`.`plan_time`, 
            0 AS amount, 
            NULL AS new_cus_id, 
            0 AS new_amount, 
            NULL AS old_cus_id, 
            0 AS old_amount, 
            `oh`.`cus_id` AS `loss_cus_id`, 
            0 AS operate_amount 
        FROM `erp_customer_churn_remark` `ccr` 
        LEFT JOIN `erp_order_header` `oh` ON oh.id = ccr.order_id 
        LEFT JOIN `erp_backend_member` `m` ON m.id = ccr.plan_teacher_id 
        LEFT JOIN `erp_store` `s` ON s.id = oh.store_id 
        WHERE (`ccr`.`reach_status`=2) 
        AND (`ccr`.`entity_id`=1) 
        AND (`ccr`.`plan_time` BETWEEN '1704038400' AND '1748534399')
    )
) `a` 
GROUP BY `a`.`teacher_id` 
ORDER BY SUM(a.amount) DESC, `a`.`teacher_id` DESC 
LIMIT 20