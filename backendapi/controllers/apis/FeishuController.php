<?php

namespace backendapi\controllers\apis;

use auth\models\feishu\App;
use backendapi\services\order\FeishuOrderService;
use backendapi\services\promote\TransferMoneyBatchService;
use backendapi\services\promote\TransferMoneyService;
use common\components\Feishu;
use common\helpers\ResultHelper;
use common\queues\feishu\ApprovalInstance;
use common\queues\feishu\MultidimensionalTableJob;
use common\queues\feishu\UserJob;
use backendapi\services\feishu\UserService;
use backendapi\services\StoreService;
use Exception;
use Yii;
use yii\web\Controller;
use services\common\FeishuExamineService;

/**
 * 飞书事件回调
 */
class FeishuController extends Controller
{
    public $enableCsrfValidation = false;
    public $comCode;

    /**
     * 回调事件
     *
     * @param string $comCode 企业标识
     * @throws Exception
     */
    public function actionCallback($comCode)
    {
        if ($comCode == 'endurise') {
            $comCode = 'chz';
        }

        $this->comCode = $comCode;
        $params = Yii::$app->request->post();

        //测试
        $logger = new \services\common\NewLogService();
        $logger->setName('DingTalkProcess');
        $logger->setLogPath(\Yii::getAlias('@backendapi/runtime/logs/FeishuCallback/' . date('Ymd') . '.log'));
        $logger->getLogger()->info('飞书回调初始数据', ['-------------------------开始--------------------------->']);
        $logger->getLogger()->info('飞书回调初始数据：', ['get' => Yii::$app->request->get(), 'post' => Yii::$app->request->post()]);

        $msg = $this->deCrypt($params['encrypt'], $comCode);
        $logger->getLogger()->info('飞书回调初始数据处理后：', $msg);
        if (isset($msg['challenge'])) {
            $return = [
                'challenge' => $msg['challenge']
            ];
            echo json_encode($return);
            $logger->getLogger()->info('飞书回调初始数据', ['-------------------------结束--------------------------->']);

            exit();
        } else {
            $logger->getLogger()->info('飞书回调初始数据', ['-------------------------结束--------------------------->']);
            $this->eventHandler($msg);
        }

        exit;
    }

    /**
     * 回调事件解密
     * 
     * @param string $encrypt_data
     * @return array
     */
    private function deCrypt($encrypt_data, $comCode)
    {
        $encrypt_key = App::find()->where(['code' => $comCode])->select(['encryptKey'])->cache(60)->scalar();
        $base64_decode_message = base64_decode($encrypt_data);
        $iv = substr($base64_decode_message, 0, 16);
        $encryped_event = substr($base64_decode_message, 16);
        $decrypt = openssl_decrypt($encryped_event, 'AES-256-CBC', hash('sha256', $encrypt_key, true), OPENSSL_RAW_DATA, $iv);
        return json_decode($decrypt, true);
    }

    /**
     * 处理回调事件
     *
     * @param $message
     */
    public function eventHandler($message)
    {
        //数据回调
        $message['ComCode'] = $this->comCode;
        if (!isset($message['schema'])) { //v1.0 版本事件
            switch ($message['event']['type']) {
                case 'approval_instance': //审批回调
                    ApprovalInstance::addJob($message);
                    break;
            }
        } else { //v2.0 版本事件
            switch ($message['header']['event_type']) {
                case 'drive.file.bitable_record_changed_v1': //多维表格回调
                    MultidimensionalTableJob::addJob($message);
                    break;
                case 'contact.user.created_v3': //员工入职
                case 'contact.user.updated_v3': //员工修改信息
                case 'contact.user.deleted_v3': //员工离职
                    UserJob::addJob($message);
                    break;
            }
        }

        return true;
    }

    /**
     * 多维表格回调
     */
    public function actionTalbeCallback()
    {
        $feishu = new FeishuExamineService();
        return $feishu->callbackMoreTableDealData(Yii::$app->request->post());
    }

    /**
     * 处理飞书扫码调店请求
     * 
     * @return array 响应数据
     */
    public function actionAdjustStore()
    {
        try {
            $userId = UserService::adjustStore();
        } catch (Exception $e) {
            Yii::warning($e->getMessage(), 'feishuAdjustStore');
            return ResultHelper::json(422, $e->getMessage());
        }

        if (!$userId) {
            return ResultHelper::json(200, '调店成功');
        } else {
            return ResultHelper::json(422, 'wxcomUserNotFound', ['userId' => $userId]);
        }
    }

    /**
     * 功能测试使用
     */
    public function actionGetTableData()
    {
        echo date('Y-m-t 23:59:59');
        exit;
        $status = 'APPROVED'; //同意
        // $status = 'REJECTED';//拒绝
        // $status = 'CANCELED';//撤销，删除
        // $status = 'PENDING';//进行中
        $data = '{
            "uuid":"714c8f569db2fcdcd240e73d39d3ac69",
            "event":{
                "app_id":"cli_a4804fd7ca71d00d",
                "approval_code":"51A5E71D-487D-44A9-A6E2-3B16019E6FCF",
                "instance_code":"49FCDB7B-CFB7-4A0B-9E16-7F469FDC1D5F",
                "instance_operate_time":"1695462798779",
                "operate_time":"1695462798779",
                "status":' . $status . ',
                "tenant_key":"138bcc1e278f9740",
                "type":"approval_instance",
                "uuid":""
            },
            "token":"t85U38vmyLBeTjdTQ4JyAfurVhjjiaqT",
            "ts":"1695462799.092823",
            "type":"event_callback"
        }';

        $message = json_decode($data, true);
        $this->comCode = 'endurise';
        $this->eventHandler($message);
        exit;


        $file_token = "W37VbcQP9ay2VGs6tUvcOX5xndg";
        // $feishu = new Feishu();
        // $data = $feishu->fileSubscribe($file_token);
        // p($data);
        // exit;

        $feishu = new Feishu();
        $data = $feishu->getChatList();
        // $data = $feishu->fileUnSubscribe($file_token);
        p($data);
        exit;
        try {
            $this->comCode = 'endurise';
            $message = '{"uuid":"e15443432724c7048fe4a01137c0276a","event":{"app_id":"cli_a4804fd7ca71d00d","approval_code":"51A5E71D-487D-44A9-A6E2-3B16019E6FCF","instance_code":"641C2AD1-3962-4AD4-A4BA-EBFFFC49507F","instance_operate_time":"1695802227584","operate_time":"1695802227584","status":"APPROVED","tenant_key":"138bcc1e278f9740","type":"approval_instance","uuid":""},"token":"t85U38vmyLBeTjdTQ4JyAfurVhjjiaqT","ts":"1695802228.115146","type":"event_callback"}';
            $message = json_decode($message, true);

            $this->eventHandler($message);
            echo 1;
        } catch (Exception $e) {
            p($e->getMessage());
        }

        exit;
    }

    public function actionTransferMoney()
    {
        $postData = Yii::$app->request->post();
        $model = new TransferMoneyService();
        try {
            $model->run($postData);
            if ($model->code == $model->success_insufficient_balance_code) {
                return ResultHelper::json($model->code, '主体：' . $model->mainBody . ',账户ID：' . $model->advertiser_id . ' 的备用金仅剩：' . $model->insufficientNalance . '元，请及时充值');
            } else {
                return ResultHelper::json($model->code, '充值成功');
            }
        } catch (Exception $e) {
            return ResultHelper::json($model->code, $e->getMessage());
        }
    }

    public function actionTransferMoneyBatch()
    {
        $postData = Yii::$app->request->post();
        $model = new TransferMoneyBatchService();
        try {
            $res = $model->run($postData);
            return ResultHelper::json($model->code, $res);
        } catch (Exception $e) {
            $res[$model->code] = ['code' => $model->code, 'msg' => $e->getMessage()];
            return ResultHelper::json($model->success_code, $res);
        }
    }

    public function actionCheckBalance()
    {
        $postData = Yii::$app->request->post();
        $model = new TransferMoneyService();
        try {
            if ($postData['isSystem'] != 'chz') {
                throw new Exception('查询余额失败，请联系信息部处理');
            }
            $content = $model->getAccountBalance();
            return ResultHelper::json(200, $content);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionOrderOperate()
    {
        $postData = Yii::$app->request->post();
        $model = new FeishuOrderService();
        $trans = Yii::$app->db->beginTransaction();
        try {
            $model->run($postData);
            Yii::$app->user->identity = null;
            $trans->commit();
            return ResultHelper::json(200, '操作成功');
        } catch (Exception $e) {
            Yii::$app->user->identity = null;
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionMsgCallback()
    {
        $postData = Yii::$app->request->post();
        Yii::$app->feishuNotice->text(json_encode($postData, 256), 'd83e649c', 'user_id');
        Yii::$app->feishuNotice->text(json_encode($postData, 256));
        echo 'success';
        exit;
    }

    /**
     * 飞书-回调配置---暂时没在用
     *
     * @throws Exception
     */
    public function actionCallbackConfig($comCode)
    {
        $this->comCode = $comCode;
        $params = Yii::$app->request->post();
        $msg = $this->deCrypt($params['encrypt'], $comCode);
        
        if (isset($msg['challenge'])) {
            $return = [
                'challenge' => $msg['challenge']
            ];
            echo json_encode($return);
            exit();
        } else {
            $this->eventHandlerTwo($msg);
        }

        exit;
    }

    /**
     * 飞书-回调配置-回调事件处理--暂时没在用
     *
     * @param $message
     */
    public function eventHandlerTwo($message)
    {
        $message['ComCode'] = $this->comCode;
        if ($message['header']['event_type'] == 'card.action.trigger') { //卡片回调
            $action = $message['event']['action'];
            $data = [
                'content' => [
                    'name' => $action['value']['interviewer_name'],
                    'phone' => $action['value']['interviewer_phone'],
                    'is_interview' => $action['form_value']['Select_mwc2697z2j'],
                    'result' => $action['form_value']['Input_9fnxvj24h6'],
                ]
            ];
            echo json_encode($data);
            exit();
        }

        return true;
    }

    /**
     * 飞书审批控件-获取门店列表
     */
    public function actionStoreList()
    {
        echo (new StoreService())->getListToFeishu(Yii::$app->request->post());
        exit;
    }
}
