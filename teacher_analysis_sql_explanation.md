# 老师业绩分析 SQL 查询详细解析

## 概述
这个SQL查询是老师业绩分析页面的核心查询，用于统计每个老师在指定时间段内的各项业绩指标，包括新客业绩、老客业绩、操作业绩等。

## 1. SQL 结构分析

### 整体架构
```
主查询 (聚合统计)
└── FROM (UNION ALL 子查询)
    ├── 子查询1: 订单业绩数据
    └── 子查询2: 流失客户数据
```

### 三层查询结构

**第一层：子查询1 - 订单业绩数据**
- 目的：获取老师的订单业绩数据
- 数据源：订单相关表
- 核心逻辑：计算新客/老客业绩、操作业绩

**第二层：子查询2 - 流失客户数据**  
- 目的：获取老师负责的流失客户数据
- 数据源：客户流失备注表
- 核心逻辑：统计流失客户信息

**第三层：主查询 - 聚合统计**
- 目的：将两个子查询的数据合并并按老师聚合
- 核心逻辑：计算各种统计指标和客单价

## 2. 表关联逻辑详解

### 子查询1的表关联关系

```sql
erp_order_header oh (订单主表)
├── LEFT JOIN erp_order_project op (订单项目表)
│   └── 关联条件: op.order_id = oh.id
├── LEFT JOIN erp_order_teacher ot (订单老师表)  
│   └── 关联条件: ot.order_id = oh.id AND ot.order_project_id = op.id
├── LEFT JOIN erp_backend_member m (员工表)
│   └── 关联条件: m.id = ot.user_id
├── LEFT JOIN erp_customer c (客户表)
│   └── 关联条件: c.id = oh.cus_id  
├── LEFT JOIN erp_store s (门店表)
│   └── 关联条件: s.id = oh.store_id
└── LEFT JOIN erp_customer_product_record cpr (划卡记录表) 🆕
    └── 关联条件: cpr.order_id = oh.id AND cpr.goods_id = op.goods_id
```

**业务含义：**
- **订单主表 → 订单项目表**：一个订单可以包含多个项目
- **订单项目表 → 订单老师表**：每个项目可以分配给多个老师（操作老师、销售老师等）
- **订单老师表 → 员工表**：获取老师的基本信息
- **订单主表 → 客户表**：获取客户信息，用于判断新老客户
- **订单主表 → 门店表**：获取门店信息
- **🆕 划卡记录表**：判断是否有划卡记录，用于计算操作业绩

### 子查询2的表关联关系

```sql
erp_customer_churn_remark ccr (客户流失备注表)
├── LEFT JOIN erp_order_header oh (订单主表)
│   └── 关联条件: oh.id = ccr.order_id
├── LEFT JOIN erp_backend_member m (员工表)
│   └── 关联条件: m.id = ccr.plan_teacher_id  
└── LEFT JOIN erp_store s (门店表)
    └── 关联条件: s.id = oh.store_id
```

## 3. 字段计算逻辑详解

### 3.1 基础金额字段

**amount (总业绩)**
```sql
IFNULL((op.received_amount + op.group_amount), 0) AS amount
```
- **业务含义**：订单项目的实收金额 + 团购金额
- **计算逻辑**：门店实际收到的钱

### 3.2 新客/老客判断逻辑

**核心判断条件**
```sql
FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d")
```

**new_cus_id (新客户ID)**
```sql
CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") 
     AND c.channel_id not in (排除渠道) 
     THEN oh.cus_id 
     ELSE NULL END
```

**new_amount (新客业绩)**
```sql
CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d")
     AND c.channel_id not in (排除渠道)
     THEN (op.sales_amount + op.group_amount) 
     ELSE 0 END
```

**old_cus_id (老客户ID)**
```sql
CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") > FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d")
     OR c.channel_id in (特殊渠道)
     THEN oh.cus_id 
     ELSE NULL END
```

**业务含义：**
- **新客户**：订单日期 = 客户首次到店日期，且不在排除渠道内
- **老客户**：订单日期 > 客户首次到店日期，或者在特殊渠道内

### 3.3 🆕 操作业绩字段 (核心新增功能)

**operate_amount (操作业绩)**
```sql
CASE WHEN cpr.id IS NOT NULL 
     THEN (op.operation_amount + op.group_amount) 
     ELSE 0 END
```

**业务含义：**
- **条件**：必须存在划卡记录 (cpr.id IS NOT NULL)
- **计算**：操作金额 + 团购金额
- **核心逻辑**：只有当客户实际使用了项目（产生划卡记录）时，才计算操作业绩

**与销售业绩的区别：**
- **销售业绩**：客户购买时就计算，不管是否使用
- **操作业绩**：客户实际使用时才计算，体现老师的实际服务量

## 4. WHERE 条件分析

### 子查询1的过滤条件
```sql
WHERE (
    oh.plan_time BETWEEN '开始时间' AND '结束时间'  -- 时间范围
    AND oh.order_status = 5                        -- 订单状态：已完成
    AND ot.type = 1                               -- 老师类型：操作老师
    AND oh.entity_id = 当前企业ID                   -- 企业隔离
)
AND s.dept_id IN (权限范围)                        -- 数据权限
AND s.dept_id IN (查询参数.dept_id)                -- 用户选择的门店
AND m.username LIKE '%查询参数.teacher_name%'      -- 老师姓名模糊查询
```

### 子查询2的过滤条件
```sql
WHERE (
    ccr.reach_status = 2                          -- 流失状态：已确认流失
    AND ccr.entity_id = 当前企业ID                  -- 企业隔离  
    AND ccr.plan_time BETWEEN '开始时间' AND '结束时间' -- 时间范围
)
-- 其他条件同子查询1
```

## 5. 聚合统计逻辑

### 5.1 基础统计字段

**date (出勤天数)**
```sql
COUNT(DISTINCT FROM_UNIXTIME(a.plan_time, "%Y-%m-%d")) AS date
```
- 统计老师在时间段内有业绩的天数

**total_amount (总业绩)**  
```sql
SUM(a.amount) AS total_amount
```
- 老师的总业绩金额

**new_amount (新客业绩)**
```sql  
SUM(a.new_amount) AS new_amount
```
- 新客户产生的业绩总和

**new_cus_count (新客到店人数)**
```sql
COUNT(DISTINCT a.new_cus_id) AS new_cus_count  
```
- 去重统计新客户数量

### 5.2 客单价计算

**new_price (新客客单价)**
```sql
ROUND(SUM(a.new_amount) / IF(COUNT(DISTINCT a.new_cus_id) > 0, COUNT(DISTINCT a.new_cus_id), 1), 2)
```
- **公式**：新客业绩 ÷ 新客人数
- **防除零**：当新客人数为0时，分母设为1

**old_price (老客客单价)**
```sql  
ROUND(SUM(a.old_amount) / IF(COUNT(DISTINCT a.old_cus_id) > 0, COUNT(DISTINCT a.old_cus_id), 1), 2)
```

**total_price (客单价-包含流失)**
```sql
ROUND(SUM(a.amount) / IF((COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)) > 0, 
                         (COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)), 1), 2)
```
- **公式**：总业绩 ÷ (新客人数 + 流失人数)

### 5.3 🆕 操作业绩统计

**operate_amount (操作业绩)**
```sql
SUM(a.operate_amount) AS operate_amount
```
- **业务含义**：有划卡记录的操作业绩总和
- **统计维度**：按老师汇总

## 6. GROUP BY 和 ORDER BY

**GROUP BY**
```sql
GROUP BY a.teacher_id
```
- 按老师ID分组，确保每个老师一条记录

**ORDER BY**  
```sql
ORDER BY SUM(a.amount) DESC, a.teacher_id DESC
```
- 按总业绩降序排列
- 业绩相同时按老师ID降序排列

## 7. 与代码的对应关系

### 7.1 参数处理 (第158-177行)
```php
$startTime = $params['start_time'] ?: strtotime(date('Y-m-1'));
$endTime = $params['end_time'] ?: strtotime(date('Y-m-d') . '23:59:59');
$entity_id = Yii::$app->user->identity->current_entity_id;
$channelIds = ChannelService::notNewCusAdsFrom(true, $entity_id);
```

### 7.2 子查询1构建 (第178-210行)
- **OrderHeader::find()->alias('oh')**：对应主表查询
- **leftJoin()系列**：对应表关联逻辑
- **new Expression()**：对应复杂字段计算
- **where()条件**：对应WHERE子句

### 7.3 子查询2构建 (第212-240行)  
- **CustomerChurnRemark::find()**：对应流失客户查询
- 字段设置为固定值或NULL，保持与子查询1结构一致

### 7.4 主查询构建 (第242-265行)
- **$subQuery1->union($subQuery2, true)**：对应UNION ALL操作
- **new Expression()聚合函数**：对应统计字段计算
- **groupBy()和orderBy()**：对应分组和排序逻辑

## 8. 业务场景应用

这个查询支持以下业务分析：

1. **老师业绩排名**：按总业绩排序，了解老师业绩表现
2. **新老客分析**：分析老师在新客开发和老客维护方面的能力  
3. **🆕 操作业绩分析**：了解老师实际服务客户的业绩，区别于销售业绩
4. **客单价分析**：评估老师的客户价值挖掘能力
5. **出勤分析**：通过出勤天数了解老师的工作积极性
6. **流失客户分析**：了解老师负责的客户流失情况

## 9. 🆕 操作业绩的业务价值

**与传统业绩统计的区别：**
- **传统业绩**：客户下单即计算，可能存在"虚高"
- **操作业绩**：客户实际消费才计算，更真实反映服务量

**业务应用场景：**
- **绩效考核**：更准确评估老师的实际工作量
- **资源配置**：根据实际服务能力分配客户资源
- **培训指导**：识别理论销售与实际服务的差距
- **客户满意度**：操作业绩高说明客户愿意实际消费服务
