<?php

namespace auth\services\data;

use auth\models\Customer;
use Exception;
use yii\db\Expression;
use yii\db\Query;
use auth\models\data\TeacherAnalysis;
use auth\models\order\CustomerChurnRemark;
use auth\models\Store;
use backendapi\services\promote\ChannelService;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\Tool;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\order\OrderProject;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\models\CustomerProductRecord;
use common\models\OrderTeacher;
use common\services\data\TeacherAnalysisService as CommonTeacherAnalysisService;
use Yii;

class TeacherAnalysisService extends CommonTeacherAnalysisService
{
    /**
     * @var TeacherAnalysis
     */
    public static $modelClass = TeacherAnalysis::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['status' => $params['status']]);
        if ($params['keyword']) {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);
        $query->with(['createdPerson', 'updatedPerson']);

        $totalCount = $query->count();
        $list = $query->all();

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 获取数据列表
     * @param $params
     * @return array
     */
    public static function getDataList($params, $isExport = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);
        $limit = ArrayHelper::getValue($params, 'limit', 20);
        $offset = ($page - 1) * $limit;

        $query = self::dataQuery($params);
        $sumQuery = clone $query;

        $totalCount = 0;
        if ($isExport) {
            if ($params['getTotal']) {
                return [[], $query->count()];
            }
        } else {
            $totalCount = $query->count();
            if (!$totalCount) {
                return [[], $totalCount];
            }
        }

        if (!$isExport) {
            $sumData = $sumQuery->groupBy('')->orderBy('')->one();
            $sumData['teacher_id'] = $sumData['dept_name'] = $sumData['status'] =  $sumData['date'] = '';
            $sumData['teacher_name'] = '合计';
            $totalCusCount = $sumData['new_cus_count'] + $sumData['loss_num'];
            $totalCusCount = $totalCusCount > 0 ? $totalCusCount : 1;
            $sumData['total_price'] = BcHelper::div($sumData['total_amount'], $totalCusCount);
        } else {
            $sumData = [];
        }

        $list = $query->offset($offset)->limit($limit)->all();
        foreach ($list as &$item) {
            $childCusCount = $item['new_cus_count'] + $item['loss_num'];
            $childCusCount = $childCusCount > 0 ? $childCusCount : 1;
            $item['total_price'] = BcHelper::div($item['total_amount'], $childCusCount);
        }

        if ($sumData) {
            array_unshift($list, $sumData);
        }
        return [$list, $totalCount];
    }

    public static function dataQuery($params)
    {
        $startTime = $params['start_time'] ?: strtotime(date('Y-m-1'));
        $endTime = $params['end_time'] ?: strtotime(date('Y-m-d') . '23:59:59');

        list($scope, $responsibleId) = Tool::getAuthScope();

        if (isset($params['dept_id'])) {
            $params['dept_id'] = Department::getManageDeptIdsByIds($params['dept_id']);
        }

        $entity_id = Yii::$app->user->identity->current_entity_id;
        //不算新客人数的渠道
        $channelIds = ChannelService::notNewCusAdsFrom(true, $entity_id);
        $newCusChannelIds = '';
        $oldCusChannelIds = '';
        if ($channelIds) {
            $newCusChannelIds = "and c.channel_id not in ({$channelIds})";
            $oldCusChannelIds = "or c.channel_id in ({$channelIds})";
        }
        // 查询订单数据
        $subQuery1 = OrderHeader::find()
            ->alias('oh')
            ->select([
                'm.id AS teacher_id',
                'm.username AS teacher_name',
                's.store_name as dept_name',
                'm.status',
                'oh.plan_time',
                new Expression('IFNULL((op.received_amount + op.group_amount), 0) AS amount'),
                new Expression('CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") ' . $newCusChannelIds . ' THEN oh.cus_id ELSE NULL END AS new_cus_id'),
                new Expression('CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") = FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") ' . $newCusChannelIds . ' THEN (op.sales_amount + op.group_amount) ELSE 0 END AS new_amount'),
                new Expression('CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") > FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") ' . $oldCusChannelIds . ' THEN oh.cus_id ELSE NULL END AS old_cus_id'),
                new Expression('CASE WHEN FROM_UNIXTIME(oh.plan_time, "%Y-%m-%d") > FROM_UNIXTIME(c.first_visit_time, "%Y-%m-%d") ' . $oldCusChannelIds . ' THEN (op.sales_amount + op.group_amount) ELSE 0 END AS old_amount'),
                new Expression('NULL AS loss_cus_id'),
                new Expression('CASE WHEN cpr.id IS NOT NULL THEN (op.operation_amount + op.group_amount) ELSE 0 END AS operate_amount')
            ])
            ->leftJoin(['op' => OrderProject::tableName()], 'op.order_id = oh.id')
            ->leftJoin(['ot' => OrderTeacher::tableName()], 'ot.order_id = oh.id AND ot.order_project_id = op.id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = ot.user_id')
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->leftJoin(['cpr' => CustomerProductRecord::tableName()], 'cpr.order_id = oh.id AND cpr.goods_id = op.goods_id')
            ->where([
                'and',
                ['between', 'oh.plan_time', $startTime, $endTime],
                ['oh.order_status' => 5],
                ['ot.type' => 1],
                ['oh.entity_id' => $entity_id]
            ])
            ->andFilterWhere(['s.dept_id' => $scope])
            ->andFilterWhere(['s.dept_id' => $params['dept_id']])
            ->andFilterWhere(['like', 'm.username', trim($params['teacher_name'])]);

        // 查询流失客户数据
        $subQuery2 = CustomerChurnRemark::find()
            ->alias('ccr')
            ->select([
                'm.id AS teacher_id',
                'm.username AS teacher_name',
                's.store_name as dept_name',
                'm.status',
                'ccr.plan_time',
                new Expression('0 AS amount'),
                new Expression('NULL AS new_cus_id'),
                new Expression('0 AS new_amount'),
                new Expression('NULL AS old_cus_id'),
                new Expression('0 AS old_amount'),
                'oh.cus_id AS loss_cus_id',
                new Expression('0 AS operate_amount')
            ])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = ccr.order_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = ccr.plan_teacher_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->where([
                'and',
                ['ccr.reach_status' => 2],
                ['ccr.entity_id' => Yii::$app->user->identity->current_entity_id],
                ['between', 'ccr.plan_time', $startTime, $endTime]
            ])
            ->andFilterWhere(['s.dept_id' => $scope])
            ->andFilterWhere(['s.dept_id' => $params['dept_id']])
            ->andFilterWhere(['like', 'm.username', trim($params['teacher_name'])]);

        // 合并子查询
        $combinedQuery = (new Query())
            ->from(['a' => $subQuery1->union($subQuery2, true)])
            ->select([
                'a.teacher_id',
                'a.teacher_name',
                'a.dept_name',
                'a.status',
                new Expression('COUNT(DISTINCT FROM_UNIXTIME(a.plan_time, "%Y-%m-%d")) AS `date`'),
                new Expression('SUM(a.amount) AS total_amount'),
                new Expression('SUM(a.new_amount) AS new_amount'),
                new Expression('COUNT(DISTINCT a.new_cus_id) AS new_cus_count'),
                new Expression('ROUND(SUM(a.new_amount) / IF(COUNT(DISTINCT a.new_cus_id) > 0, COUNT(DISTINCT a.new_cus_id), 1), 2) AS new_price'),
                new Expression('SUM(a.old_amount) AS old_amount'),
                new Expression('COUNT(DISTINCT a.old_cus_id) AS old_cus_count'),
                new Expression('ROUND(SUM(a.old_amount) / IF(COUNT(DISTINCT a.old_cus_id) > 0, COUNT(DISTINCT a.old_cus_id), 1), 2) AS old_price'),
                new Expression('COUNT(DISTINCT a.loss_cus_id) AS loss_num'),
                new Expression('ROUND(SUM(a.amount) / IF((COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)) > 0, (COUNT(DISTINCT a.new_cus_id) + COUNT(DISTINCT a.loss_cus_id)), 1), 2) AS total_price'),
                new Expression('SUM(a.operate_amount) AS operate_amount')
            ]);

        $combinedQuery->groupBy('a.teacher_id')->orderBy('SUM(a.amount) DESC,a.teacher_id DESC');

        return $combinedQuery;
    }
}
